import 'dart:async';
import 'package:cross_cache/cross_cache.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_chat_core/flutter_chat_core.dart';
import 'package:flutter_chat_ui/flutter_chat_ui.dart';
import 'package:flyer_chat_image_message/flyer_chat_image_message.dart';
import 'package:flyer_chat_text_message/flyer_chat_text_message.dart';
import 'package:flyer_chat_text_stream_message/flyer_chat_text_stream_message.dart';
import 'package:firebase_ai/firebase_ai.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';

import '../utils/gemini_stream_manager.dart';
import '../hive_chat_controller.dart';
import '../widgets/composer_action_bar.dart';
import '../services/firestore.dart';
import '../services/chat_title_generator.dart';
// import '../models/models.dart' as models;

// Define the shared animation duration
const Duration _kChunkAnimationDuration = Duration(milliseconds: 350);

class GeminiChat extends StatefulWidget {
  final String geminiApiKey;
  final String chatId;

  const GeminiChat({
    super.key,
    required this.geminiApiKey,
    required this.chatId,
  });

  @override
  GeminiChatState createState() => GeminiChatState();
}

class GeminiChatState extends State<GeminiChat> {
  final _uuid = const Uuid();
  final _crossCache = CrossCache();
  final _scrollController = ScrollController();
  final _chatController = HiveChatController();

  final _currentUser = const User(id: 'me');
  final _agent = const User(id: 'agent');

  late final GenerativeModel _model;
  late ChatSession _chatSession;

  late final GeminiStreamManager _streamManager;
  late final ChatTitleGenerator _titleGenerator;

  // Track if title generation has been triggered for this chat
  bool _titleGenerationTriggered = false;

  // Store scroll state per stream ID
  final Map<String, double> _initialScrollExtents = {};
  final Map<String, bool> _reachedTargetScroll = {};

  // Streaming state management
  bool _isStreaming = false;
  StreamSubscription? _currentStreamSubscription;
  String? _currentStreamId;

  @override
  void initState() {
    super.initState();
    _streamManager = GeminiStreamManager(
      chatController: _chatController,
      chunkAnimationDuration: _kChunkAnimationDuration,
    );

    _model = FirebaseAI.googleAI().generativeModel(
      model: 'gemini-2.5-flash',
      generationConfig: GenerationConfig(temperature: 0.7),
      safetySettings: [
        SafetySetting(HarmCategory.dangerousContent, HarmBlockThreshold.none, HarmBlockMethod.severity),
      ],
    );

    _titleGenerator = ChatTitleGenerator(geminiApiKey: widget.geminiApiKey);

    // Load messages from Firestore and initialize chat session
    _loadMessagesAndInitializeChat();
  }

  Future<void> _loadMessagesAndInitializeChat() async {
    try {
      final currentUser = auth.FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        // Load messages from Firestore
        final firestoreMessages = await FirestoreService.getMessages(
          widget.chatId,
          currentUser.uid,
        );

        // Convert Firestore messages to flutter_chat_core messages
        final chatMessages = <Message>[];
        final chatHistory = <Content>[];

        for (final firestoreMessage in firestoreMessages) {
          if (firestoreMessage.type == 'text' &&
              firestoreMessage.textContent != null) {
            // Create flutter_chat_core message
            final authorId = firestoreMessage.userId != null ? 'me' : 'agent';
            final chatMessage = TextMessage(
              id: firestoreMessage.id ?? _uuid.v4(),
              authorId: authorId,
              createdAt: firestoreMessage.postedDate,
              text: firestoreMessage.textContent!,
              metadata: firestoreMessage.metadata,
            );
            chatMessages.add(chatMessage);

            // Add to Gemini chat history (both user and AI messages for context)
            if (firestoreMessage.userId != null) {
              // User message
              chatHistory.add(Content.text(firestoreMessage.textContent!));
            } else if (firestoreMessage.systemPersonaId == 'gemini-ai') {
              // AI response
              chatHistory.add(
                Content.model([TextPart(firestoreMessage.textContent!)]),
              );
            }
          } else if (firestoreMessage.type == 'image' &&
              firestoreMessage.imageUrl != null) {
            // Create flutter_chat_core image message
            final authorId = firestoreMessage.userId != null ? 'me' : 'agent';
            final chatMessage = ImageMessage(
              id: firestoreMessage.id ?? _uuid.v4(),
              authorId: authorId,
              createdAt: firestoreMessage.postedDate,
              source: firestoreMessage.imageUrl!,
              metadata: firestoreMessage.metadata,
            );
            chatMessages.add(chatMessage);
          }
        }

        // Set messages in chat controller
        await _chatController.setMessages(chatMessages);

        // Initialize Gemini chat session with history
        _chatSession = _model.startChat(history: chatHistory);
      }
    } catch (e) {
      debugPrint('Error loading messages: $e');
      // Initialize empty chat session if loading fails
      _chatSession = _model.startChat();
    }
  }

  @override
  void dispose() {
    _currentStreamSubscription?.cancel();
    _streamManager.dispose();
    _chatController.dispose();
    _scrollController.dispose();
    _crossCache.dispose();
    super.dispose();
  }

  void _stopCurrentStream() {
    if (_currentStreamSubscription != null && _currentStreamId != null) {
      _currentStreamSubscription!.cancel();
      _currentStreamSubscription = null;

      setState(() {
        _isStreaming = false;
      });

      // Mark the current stream as stopped/errored
      if (_currentStreamId != null) {
        _streamManager.errorStream(_currentStreamId!, 'Stream stopped by user');
        _currentStreamId = null;
      }
    }
  }

  void _handleStreamError(
    String streamId,
    dynamic error,
    TextStreamMessage? streamMessage,
  ) async {
    debugPrint('Generation error for $streamId: $error');

    // Stream failed (only if message was created)
    if (streamMessage != null) {
      await _streamManager.errorStream(streamId, error);
    }

    // Reset streaming state
    if (mounted) {
      setState(() {
        _isStreaming = false;
      });
    }
    _currentStreamSubscription = null;
    _currentStreamId = null;

    // Clean up scroll state for this stream ID
    _initialScrollExtents.remove(streamId);
    _reachedTargetScroll.remove(streamId);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(title: const Text('New Chat')),
      body: ChangeNotifierProvider.value(
        value: _streamManager,
        child: Chat(
          builders: Builders(
            chatAnimatedListBuilder: (context, itemBuilder) {
              return ChatAnimatedList(
                scrollController: _scrollController,
                itemBuilder: itemBuilder,
                shouldScrollToEndWhenAtBottom: false,
              );
            },
            imageMessageBuilder:
                (
                  context,
                  message,
                  index, {
                  required bool isSentByMe,
                  MessageGroupStatus? groupStatus,
                }) => FlyerChatImageMessage(
                  message: message,
                  index: index,
                  showTime: false,
                  showStatus: false,
                ),
            composerBuilder: (context) => CustomComposer(
              isStreaming: _isStreaming,
              onStop: _stopCurrentStream,
              topWidget: ComposerActionBar(
                buttons: [
                  ComposerActionButton(
                    icon: Icons.delete_sweep,
                    title: 'Clear all',
                    onPressed: () {
                      _chatController.setMessages([]);
                      _chatSession = _model.startChat();
                    },
                    destructive: true,
                  ),
                ],
              ),
            ),
            textMessageBuilder:
                (
                  context,
                  message,
                  index, {
                  required bool isSentByMe,
                  MessageGroupStatus? groupStatus,
                }) => FlyerChatTextMessage(
                  message: message,
                  index: index,
                  showTime: false,
                  showStatus: false,
                  receivedBackgroundColor: Colors.transparent,
                  padding: message.authorId == _agent.id
                      ? EdgeInsets.zero
                      : const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 10,
                        ),
                ),
            textStreamMessageBuilder:
                (
                  context,
                  message,
                  index, {
                  required bool isSentByMe,
                  MessageGroupStatus? groupStatus,
                }) {
                  // Watch the manager for state updates
                  final streamState = context
                      .watch<GeminiStreamManager>()
                      .getState(message.streamId);
                  // Return the stream message widget, passing the state
                  return FlyerChatTextStreamMessage(
                    message: message,
                    index: index,
                    streamState: streamState,
                    chunkAnimationDuration: _kChunkAnimationDuration,
                    showTime: false,
                    showStatus: false,
                    receivedBackgroundColor: Colors.transparent,
                    padding: message.authorId == _agent.id
                        ? EdgeInsets.zero
                        : const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 10,
                          ),
                  );
                },
          ),
          chatController: _chatController,
          crossCache: _crossCache,
          currentUserId: _currentUser.id,
          onAttachmentTap: _handleAttachmentTap,
          onMessageSend: _handleMessageSend,
          resolveUser: (id) => Future.value(switch (id) {
            'me' => _currentUser,
            'agent' => _agent,
            _ => null,
          }),
          theme: ChatTheme.fromThemeData(theme),
        ),
      ),
    );
  }

  void _handleMessageSend(String text) async {
    final currentUser = auth.FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    final messageId = _uuid.v4();
    final now = DateTime.now().toUtc();

    // Create flutter_chat_core message
    final chatMessage = TextMessage(
      id: messageId,
      authorId: _currentUser.id,
      createdAt: now,
      text: text,
      metadata: isOnlyEmoji(text) ? {'isOnlyEmoji': true} : null,
    );

    // Add to chat controller
    await _chatController.insertMessage(chatMessage);

    // Create Firestore message and persist it
    try {
      final firestoreMessage = FirestoreService.createMessageFromText(
        chatId: widget.chatId,
        chatOwnerId: currentUser.uid,
        userId: currentUser.uid,
        systemPersonaId: null,
        textContent: text,
        postedDate: now,
        metadata: isOnlyEmoji(text) ? {'isOnlyEmoji': true} : null,
      );

      await FirestoreService.saveMessage(firestoreMessage);
    } catch (e) {
      debugPrint('Error saving user message to Firestore: $e');
    }

    final content = Content.text(text);
    _sendContent(content);
  }

  void _handleAttachmentTap() async {
    final currentUser = auth.FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    final picker = ImagePicker();
    final image = await picker.pickImage(source: ImageSource.gallery);

    if (image == null) return;

    await _crossCache.downloadAndSave(image.path);

    final messageId = _uuid.v4();
    final now = DateTime.now().toUtc();

    // Create flutter_chat_core message
    final chatMessage = ImageMessage(
      id: messageId,
      authorId: _currentUser.id,
      createdAt: now,
      source: image.path,
    );

    // Add to chat controller
    await _chatController.insertMessage(chatMessage);

    // Create Firestore message and persist it
    try {
      final firestoreMessage = FirestoreService.createMessageFromImage(
        chatId: widget.chatId,
        chatOwnerId: currentUser.uid,
        userId: currentUser.uid,
        systemPersonaId: null,
        imageUrl: image.path, // In a real app, you'd upload to storage first
        postedDate: now,
        metadata: null,
      );

      await FirestoreService.saveMessage(firestoreMessage);
    } catch (e) {
      debugPrint('Error saving image message to Firestore: $e');
    }

    final bytes = await _crossCache.get(image.path);
    final content = Content.data('image/jpeg', bytes);
    _sendContent(content);
  }

  void _sendContent(Content content) async {
    // Generate a unique ID for the stream
    final streamId = _uuid.v4();
    _currentStreamId = streamId;
    TextStreamMessage? streamMessage;
    var isFirstChunk = true;

    // Store scroll state per stream ID
    _reachedTargetScroll[streamId] = false;

    // Set streaming state
    setState(() {
      _isStreaming = true;
    });

    try {
      final response = _chatSession.sendMessageStream(content);

      // Create a stream subscription that can be cancelled
      _currentStreamSubscription = response.listen(
        (chunk) async {
          if (chunk.text != null) {
            final textChunk = chunk.text!;
            if (textChunk.isEmpty) return; // Skip empty chunks

            if (isFirstChunk) {
              isFirstChunk = false;

              // Create and insert the message ON the first chunk
              streamMessage = TextStreamMessage(
                id: streamId,
                authorId: _agent.id,
                createdAt: DateTime.now().toUtc(),
                streamId: streamId,
              );
              await _chatController.insertMessage(streamMessage!);
              _streamManager.startStream(streamId, streamMessage!);
            }

            // Ensure stream message exists before adding chunk
            if (streamMessage == null) return;

            // Send chunk to the manager - this triggers notifyListeners
            _streamManager.addChunk(streamId, textChunk);

            // Schedule scroll check after the frame rebuilds
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (!_scrollController.hasClients || !mounted) return;

              // Retrieve state for this specific stream
              var initialExtent = _initialScrollExtents[streamId];
              final reachedTarget = _reachedTargetScroll[streamId] ?? false;

              if (reachedTarget) return; // Already scrolled to target

              // Store initial extent after first chunk caused rebuild
              initialExtent ??= _initialScrollExtents[streamId] =
                  _scrollController.position.maxScrollExtent;

              // Only scroll if the list is scrollable
              if (initialExtent > 0) {
                // Calculate target scroll position (copied from original logic)
                final targetScroll =
                    initialExtent + // Use the stored initial extent
                    _scrollController.position.viewportDimension -
                    MediaQuery.of(context).padding.bottom -
                    168; // height of the composer + height of the app bar + visual buffer of 8

                if (_scrollController.position.maxScrollExtent > targetScroll) {
                  _scrollController.animateTo(
                    targetScroll,
                    duration: const Duration(milliseconds: 250),
                    curve: Curves.linearToEaseOut,
                  );
                  // Mark that we've reached the target for this stream
                  _reachedTargetScroll[streamId] = true;
                } else {
                  // If we haven't reached target position yet, scroll to bottom
                  _scrollController.animateTo(
                    _scrollController.position.maxScrollExtent,
                    duration: const Duration(milliseconds: 250),
                    curve: Curves.linearToEaseOut,
                  );
                }
              }
            });
          }
        },
        onDone: () async {
          // Stream completed successfully (only if message was created)
          if (streamMessage != null) {
            // Get the complete text BEFORE calling completeStream (which cleans up the data)
            final completeText = _streamManager.getAccumulatedText(streamId);

            // Save AI response to Firestore before completing the stream
            try {
              final currentUser = auth.FirebaseAuth.instance.currentUser;
              if (currentUser != null && completeText.isNotEmpty) {
                final firestoreMessage = FirestoreService.createMessageFromText(
                  chatId: widget.chatId,
                  chatOwnerId: currentUser.uid,
                  userId: null, // AI message, so no userId
                  systemPersonaId: 'gemini-ai',
                  textContent: completeText,
                  postedDate:
                      streamMessage!.createdAt ?? DateTime.now().toUtc(),
                  metadata: null,
                );

                await FirestoreService.saveMessage(firestoreMessage);

                // Trigger title generation if this is the first AI response
                _triggerTitleGenerationIfNeeded(currentUser.uid);
              }
            } catch (e) {
              debugPrint('Error saving AI response to Firestore: $e');
            }

            // Now complete the stream (this will clean up the accumulated text)
            await _streamManager.completeStream(streamId);
          }

          // Reset streaming state
          if (mounted) {
            setState(() {
              _isStreaming = false;
            });
          }
          _currentStreamSubscription = null;
          _currentStreamId = null;

          // Clean up scroll state for this stream ID
          _initialScrollExtents.remove(streamId);
          _reachedTargetScroll.remove(streamId);
        },
        onError: (error) async {
          _handleStreamError(streamId, error, streamMessage);
        },
      );
    } catch (error) {
      // Catch other potential errors during stream processing
      _handleStreamError(streamId, error, streamMessage);
    }
  }

  /// Triggers title generation if this is the first AI response and title needs updating
  void _triggerTitleGenerationIfNeeded(String userId) {
    if (!_titleGenerationTriggered) {
      _titleGenerationTriggered = true;

      // Run title generation in background without blocking UI
      _generateAndUpdateChatTitle(userId).catchError((error) {
        debugPrint('Error generating title for chat ${widget.chatId}: $error');
      });
    }
  }

  /// Generates and updates the chat title based on current messages
  Future<void> _generateAndUpdateChatTitle(String userId) async {
    try {
      // Get current messages for the chat
      final messages = await FirestoreService.getMessages(
        widget.chatId,
        userId,
      );

      if (messages.isNotEmpty) {
        // Generate title using Gemini AI
        final newTitle = await _titleGenerator.generateTitle(messages);

        // Update the chat title in Firestore
        await FirestoreService.updateChatTitle(widget.chatId, newTitle);

        debugPrint('Generated and updated chat title: $newTitle');
      }
    } catch (e) {
      debugPrint('Error generating title for chat ${widget.chatId}: $e');
    }
  }
}

class CustomComposer extends StatefulWidget {
  final Widget? topWidget;
  final bool isStreaming;
  final VoidCallback? onStop;

  const CustomComposer({
    super.key,
    this.topWidget,
    this.isStreaming = false,
    this.onStop,
  });

  @override
  State<CustomComposer> createState() => _CustomComposerState();
}

class _CustomComposerState extends State<CustomComposer> {
  final _key = GlobalKey();
  late final TextEditingController _textController;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController();
    _focusNode = FocusNode();
    _focusNode.onKeyEvent = _handleKeyEvent;
    WidgetsBinding.instance.addPostFrameCallback((_) => _measure());
  }

  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    // Check for Shift+Enter
    if (event is KeyDownEvent &&
        event.logicalKey == LogicalKeyboardKey.enter &&
        HardwareKeyboard.instance.isShiftPressed) {
      _handleSubmitted(_textController.text);
      return KeyEventResult.handled;
    }
    return KeyEventResult.ignored;
  }

  @override
  void didUpdateWidget(covariant CustomComposer oldWidget) {
    super.didUpdateWidget(oldWidget);
    WidgetsBinding.instance.addPostFrameCallback((_) => _measure());
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bottomSafeArea = MediaQuery.of(context).padding.bottom;
    final onAttachmentTap = context.read<OnAttachmentTapCallback?>();
    final theme = context.select(
      (ChatTheme t) => (
        bodyMedium: t.typography.bodyMedium,
        onSurface: t.colors.onSurface,
        surfaceContainerHigh: t.colors.surfaceContainerHigh,
        surfaceContainerLow: t.colors.surfaceContainerLow,
      ),
    );

    return Positioned(
      left: 0,
      right: 0,
      bottom: 0,
      child: ClipRect(
        child: Container(
          key: _key,
          color: theme.surfaceContainerLow,
          child: Column(
            children: [
              if (widget.topWidget != null) widget.topWidget!,
              Padding(
                padding: EdgeInsets.only(
                  bottom: bottomSafeArea,
                ).add(const EdgeInsets.all(8.0)),
                child: Row(
                  children: [
                    onAttachmentTap != null
                        ? IconButton(
                            icon: const Icon(Icons.attachment),
                            color: theme.onSurface.withValues(alpha: 0.5),
                            onPressed: onAttachmentTap,
                          )
                        : const SizedBox.shrink(),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextField(
                        controller: _textController,
                        decoration: InputDecoration(
                          hintText: 'Type a message',
                          hintStyle: theme.bodyMedium.copyWith(
                            color: theme.onSurface.withValues(alpha: 0.5),
                          ),
                          border: const OutlineInputBorder(
                            borderSide: BorderSide.none,
                            borderRadius: BorderRadius.all(Radius.circular(24)),
                          ),
                          filled: true,
                          fillColor: theme.surfaceContainerHigh.withValues(
                            alpha: 0.8,
                          ),
                          hoverColor: Colors.transparent,
                        ),
                        style: theme.bodyMedium.copyWith(
                          color: theme.onSurface,
                        ),
                        onSubmitted: _handleSubmitted,
                        textInputAction: TextInputAction.newline,
                        autocorrect: true,
                        autofocus: false,
                        textCapitalization: TextCapitalization.sentences,
                        focusNode: _focusNode,
                        minLines: 1,
                        maxLines: 3,
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: widget.isStreaming
                          ? const Icon(Icons.stop_circle)
                          : const Icon(Icons.send),
                      color: theme.onSurface.withValues(alpha: 0.5),
                      onPressed: widget.isStreaming
                          ? widget.onStop
                          : () => _handleSubmitted(_textController.text),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _measure() {
    if (!mounted) return;

    final renderBox = _key.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final height = renderBox.size.height;
      final bottomSafeArea = MediaQuery.of(context).padding.bottom;

      context.read<ComposerHeightNotifier>().setHeight(height - bottomSafeArea);
    }
  }

  void _handleSubmitted(String text) {
    if (text.isNotEmpty) {
      context.read<OnMessageSendCallback?>()?.call(text);
      _textController.clear();
    }
  }
}
